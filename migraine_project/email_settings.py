import os

from django.conf import settings

# Add email settings to Django settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', '***********')
EMAIL_PORT = os.getenv('EMAIL_PORT', 25)
EMAIL_USE_TLS = False
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
DEFAULT_FROM_EMAIL = 'Migraine Forecast <<EMAIL>>'

# For development/testing, you can use the console backend
# if settings.DEBUG:
#     EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
