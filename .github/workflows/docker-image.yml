name: Docker Image CI

on:
  push:
    branches: [ "main" ]

jobs:
  build:
    runs-on: self-hosted
    environment: latest
    steps:
      -
        name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          platforms: linux/arm64
#           platforms: linux/amd64,linux/arm64
          tags: alexandrosm77/migraine_forecast:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: self-hosted
    environment: latest
    steps:
      -
        name: Checkout repository
        uses: actions/checkout@v4

      -
        name: Install SSH client if needed
        run: |
          which scp || apt-get update && apt-get install -y openssh-client

      -
        name: Set up SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      -
        name: Copy file via direct SCP
        run: |
          scp -i ~/.ssh/id_rsa deploy.sh ${{ secrets.SSH_USERNAME }}@${{ secrets.SSH_HOST }}:/home/<USER>/migraine/

      -
        name: SSH into docker host and run deployment script
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            chmod +x /home/<USER>/migraine/deploy.sh
            /home/<USER>/migraine/deploy.sh --dockerhub_username ${{ vars.DOCKERHUB_USERNAME }} --dockerhub_token ${{ secrets.DOCKERHUB_TOKEN }}
