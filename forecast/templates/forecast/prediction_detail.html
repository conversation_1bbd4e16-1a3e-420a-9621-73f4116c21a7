{% extends 'forecast/base.html' %}

{% block title %}Prediction Details - Migraine Forecast{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Prediction Details</h1>
        <a href="{% url 'forecast:prediction_list' %}" class="btn btn-outline-secondary">Back to Predictions</a>
    </div>
    
    <div class="row">
        <!-- Prediction Info -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Prediction Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Location:</strong> {{ prediction.location.city }}, {{ prediction.location.country }}</p>
                    <p><strong>Prediction Made:</strong> {{ prediction.prediction_time|date:"F j, Y, g:i a" }}</p>
                    <p><strong>Target Time Window:</strong> {{ prediction.target_time_start|date:"F j, Y, g:i a" }} - {{ prediction.target_time_end|date:"g:i a" }}</p>
                    <p>
                        <strong>Migraine Probability:</strong>
                        {% if prediction.probability == 'HIGH' %}
                        <span class="badge bg-danger">High</span>
                        {% elif prediction.probability == 'MEDIUM' %}
                        <span class="badge bg-warning text-dark">Medium</span>
                        {% else %}
                        <span class="badge bg-success">Low</span>
                        {% endif %}
                    </p>
                    <p><strong>Notification Sent:</strong> {{ prediction.notification_sent|yesno:"Yes,No" }}</p>

                    {% if prediction.weather_factors %}
                    <hr>
                    <h6 class="mb-3">Weather Factors</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                {% if prediction.weather_factors.cloud_cover is not None %}
                                <tr>
                                    <td><strong>Cloud Cover:</strong></td>
                                    <td>{{ prediction.weather_factors.cloud_cover }}</td>
                                </tr>
                                {% endif %}

                                {% if prediction.weather_factors.humidity_extreme is not None %}
                                <tr>
                                    <td><strong>Humidity Extreme:</strong></td>
                                    <td>{{ prediction.weather_factors.humidity_extreme }}</td>
                                </tr>
                                {% endif %}

                                {% if prediction.weather_factors.precipitation is not None %}
                                <tr>
                                    <td><strong>Precipitation:</strong></td>
                                    <td>{{ prediction.weather_factors.precipitation }}</td>
                                </tr>
                                {% endif %}

                                {% if prediction.weather_factors.pressure_change is not None %}
                                <tr>
                                    <td><strong>Pressure Change:</strong></td>
                                    <td>{{ prediction.weather_factors.pressure_change }}</td>
                                </tr>
                                {% endif %}

                                {% if prediction.weather_factors.pressure_low is not None %}
                                <tr>
                                    <td><strong>Pressure Low:</strong></td>
                                    <td>{{ prediction.weather_factors.pressure_low }}</td>
                                </tr>
                                {% endif %}

                                {% if prediction.weather_factors.temperature_change is not None %}
                                <tr>
                                    <td><strong>Temperature Change:</strong></td>
                                    <td>{{ prediction.weather_factors.temperature_change }}</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Weather Conditions -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Weather Conditions</h5>
                </div>
                <div class="card-body">
                    <p><strong>Temperature:</strong> {{ prediction.forecast.temperature|floatformat:1 }}°C</p>
                    <p><strong>Humidity:</strong> {{ prediction.forecast.humidity|floatformat:0 }}%</p>
                    <p><strong>Barometric Pressure:</strong> {{ prediction.forecast.pressure|floatformat:1 }} hPa</p>
                    <p><strong>Wind Speed:</strong> {{ prediction.forecast.wind_speed|floatformat:1 }} km/h</p>
                    <p><strong>Precipitation:</strong> {{ prediction.forecast.precipitation|floatformat:1 }} mm</p>
                    <p><strong>Cloud Cover:</strong> {{ prediction.forecast.cloud_cover|floatformat:0 }}%</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Migraine Prevention Tips -->
    {% if prediction.probability == 'HIGH' or prediction.probability == 'MEDIUM' %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Migraine Prevention Tips</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Immediate Actions</h6>
                    <ul>
                        <li>Take prescribed medication if available</li>
                        <li>Stay hydrated - drink plenty of water</li>
                        <li>Find a quiet, dark environment if possible</li>
                        <li>Apply cold or warm compress to your head or neck</li>
                        <li>Practice relaxation techniques or meditation</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Preventive Measures</h6>
                    <ul>
                        <li>Maintain regular sleep schedule</li>
                        <li>Avoid known dietary triggers</li>
                        <li>Limit caffeine and alcohol intake</li>
                        <li>Stay indoors if extreme weather is a trigger</li>
                        <li>Consider using air purifiers or humidifiers</li>
                    </ul>
                </div>
            </div>
            <div class="alert alert-info mt-3">
                <small>Note: These are general suggestions and not medical advice. Please consult with your healthcare provider for personalized recommendations.</small>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
