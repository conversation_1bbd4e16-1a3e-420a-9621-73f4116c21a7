{% extends 'forecast/base.html' %}

{% block title %}Dashboard - Migraine Forecast{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Dashboard</h1>
    
    {% if not locations %}
    <div class="alert alert-info">
        <p>You haven't added any locations yet. Add a location to start receiving migraine forecasts.</p>
        <a href="{% url 'forecast:location_add' %}" class="btn btn-primary">Add Location</a>
    </div>
    {% else %}
    
    <!-- High Risk Alerts -->
    {% if upcoming_high_risk %}
    <div class="card mb-4 border-danger">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">⚠️ High Migraine Risk Alert</h5>
        </div>
        <div class="card-body">
            <p>High migraine probability detected in the next 24 hours:</p>
            <ul class="list-group">
                {% for prediction in upcoming_high_risk %}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <strong>{{ prediction.location.city }}, {{ prediction.location.country }}</strong>
                        <span class="text-muted d-block">{{ prediction.target_time_start|date:"F j, g:i a" }} - {{ prediction.target_time_end|date:"g:i a" }}</span>
                    </div>
                    <a href="{% url 'forecast:prediction_detail' prediction.id %}" class="btn btn-sm btn-outline-danger">Details</a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}
    
    <!-- Locations Summary -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Your Locations</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for location in locations %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ location.city }}</h5>
                            <h6 class="card-subtitle mb-2 text-muted">{{ location.country }}</h6>
                            <p class="card-text">
                                <small>Lat: {{ location.latitude|floatformat:2 }}, Long: {{ location.longitude|floatformat:2 }}</small>
                            </p>
                            <a href="{% url 'forecast:location_detail' location.id %}" class="btn btn-sm btn-primary">View Details</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100 border-dashed">
                        <div class="card-body d-flex justify-content-center align-items-center">
                            <a href="{% url 'forecast:location_add' %}" class="btn btn-outline-primary">
                                <i class="bi bi-plus-lg"></i> Add New Location
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Predictions -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Recent Predictions</h5>
        </div>
        <div class="card-body">
            {% if recent_predictions %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Location</th>
                            <th>Time Window</th>
                            <th>Probability</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prediction in recent_predictions %}
                        <tr>
                            <td>{{ prediction.location.city }}</td>
                            <td>{{ prediction.target_time_start|date:"M d, H:i" }} - {{ prediction.target_time_end|date:"H:i" }}</td>
                            <td>
                                {% if prediction.probability == 'HIGH' %}
                                <span class="badge bg-danger">High</span>
                                {% elif prediction.probability == 'MEDIUM' %}
                                <span class="badge bg-warning text-dark">Medium</span>
                                {% else %}
                                <span class="badge bg-success">Low</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'forecast:prediction_detail' prediction.id %}" class="btn btn-sm btn-outline-secondary">Details</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="text-end">
                <a href="{% url 'forecast:prediction_list' %}" class="btn btn-link">View All Predictions</a>
            </div>
            {% else %}
            <p class="text-muted">No recent predictions available.</p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .border-dashed {
        border-style: dashed;
    }
</style>
{% endblock %}
