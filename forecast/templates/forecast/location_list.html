{% extends 'forecast/base.html' %}

{% block title %}Locations - Migraine Forecast{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Your Locations</h1>
        <a href="{% url 'forecast:location_add' %}" class="btn btn-primary">Add Location</a>
    </div>
    
    {% if locations %}
    <div class="row">
        {% for location in locations %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ location.city }}</h5>
                    <h6 class="card-subtitle mb-2 text-muted">{{ location.country }}</h6>
                    <p class="card-text">
                        <small>Latitude: {{ location.latitude|floatformat:4 }}</small><br>
                        <small>Longitude: {{ location.longitude|floatformat:4 }}</small><br>
                        <small>Added: {{ location.created_at|date:"F j, Y" }}</small>
                    </p>
                </div>
                <div class="card-footer bg-transparent d-flex justify-content-between">
                    <a href="{% url 'forecast:location_detail' location.id %}" class="btn btn-sm btn-outline-primary">View Details</a>
                    <a href="{% url 'forecast:location_delete' location.id %}" class="btn btn-sm btn-outline-danger">Delete</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="alert alert-info">
        <p>You haven't added any locations yet. Add a location to start receiving migraine forecasts.</p>
    </div>
    {% endif %}
</div>
{% endblock %}
